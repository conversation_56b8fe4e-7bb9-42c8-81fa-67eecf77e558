import 'package:flutter/material.dart';
import 'package:flutter_app_badger/flutter_app_badger.dart';
import 'package:quycky/core/services/storage/storage_sharedpreferences_client_implementation.dart';
import 'package:quycky/core/utils/app_storage_keys.dart';
import 'package:quycky/core/utils/type_converters.dart';

class SystemBadgeService {
  static Future<int> getCurrentSystemBadgeCurrentCount() async {
    final storage = StorageSharedPreferencesClientImplementation();
    int currentBadgeCount =
        dynamicToInt(storage.read(AppStorageKeys.systemBadgeCount));
    return currentBadgeCount;
  }

  static void updateSystemBadgeCount(int count) async {
    final storage = StorageSharedPreferencesClientImplementation();
    await storage.write(AppStorageKeys.systemBadgeCount, count.toString());
  }

  static void updateBadgeCount(int count) async {
    bool isSupported = await FlutterAppBadger.isAppBadgeSupported();

    if (isSupported) {
      if (count > 0) {
        final finalCount = await getCurrentSystemBadgeCurrentCount() + count;
        debugPrint(
            'Glória a Deus!!!\\o/==> 🔔 Badge count updated to $finalCount');
        FlutterAppBadger.updateBadgeCount(finalCount);
        updateSystemBadgeCount(finalCount);
      } else {
        removeBadge();
      }
    }
  }

  static void removeBadge() async {
    bool isSupported = await FlutterAppBadger.isAppBadgeSupported();

    if (isSupported) {
      FlutterAppBadger.removeBadge();
      updateSystemBadgeCount(0);
    }
  }
}
